@extends('backend.layouts.master')

@section('title','Lamart || Picker Orders')

@push('styles')
<script src="https://unpkg.com/@zxing/library@latest/umd/index.min.js"></script>
@endpush

@section('main-content')
 <!-- DataTales Example -->
 <div class="card shadow mb-4">
     <div class="row">
         <div class="col-md-12">
            @include('backend.layouts.notification')
         </div>
     </div>
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary float-left">My Assigned Orders</h6>
      <div class="float-right">
        <button class="btn btn-info btn-sm mr-2" id="scanOrderBarcodeBtn">
          <i class="fas fa-qrcode"></i> Scan Order Barcode
        </button>
        <select id="statusFilter" class="form-control form-control-sm" style="width: auto; display: inline-block;">
          <option value="">All Orders</option>
          <option value="sent_to_warehouse">Sent to Warehouse</option>
          <option value="processing">Being Packed</option>
          <option value="shipped">Completed</option>
        </select>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        @if(count($orders)>0)
        <table class="table table-bordered" id="order-dataTable" width="100%" cellspacing="0">
          <thead>
            <tr>
              <th>Order No.</th>
              <th>Customer</th>
              <th>Items</th>
              <th>Total Amount</th>
              <th>Delivery Method</th>
              <th>Status</th>
              <th>Assigned Date</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            @foreach($orders as $order)
                <tr data-status="{{$order->status}}">
                    <td>{{$order->order_number}}</td>
                    <td>{{$order->first_name.' '.$order->last_name}}</td>
                    <td>
                      <span class="badge badge-info">{{$order->cart_info->count()}} items</span>
                    </td>
                    <td>${{number_format($order->total_amount,2)}}</td>
                    <td>
                      <span class="badge badge-secondary">{{ucfirst($order->delivery_method ?? 'N/A')}}</span>
                    </td>
                    <td>
                        @if($order->status=='sent_to_warehouse')
                            <span class="badge badge-warning">Sent to Warehouse</span>
                        @elseif($order->status=='processing')
                            <span class="badge badge-info">Being Packed</span>
                        @elseif($order->status=='shipped')
                            <span class="badge badge-success">Completed</span>
                        @else
                            <span class="badge badge-primary">{{ucfirst($order->status)}}</span>
                        @endif
                    </td>
                    <td>{{$order->created_at->format('M d, Y')}}</td>
                    <td>
                        <a href="{{route('picker.orders.show',$order->id)}}" class="btn btn-primary btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="view" data-placement="bottom"><i class="fas fa-eye"></i></a>

                        @if($order->status == 'sent_to_warehouse')
                        <button class="btn btn-success btn-sm start-packing" data-id="{{$order->id}}" style="height:30px; width:auto;" data-toggle="tooltip" title="Start Packing" data-placement="bottom">
                          <i class="fas fa-play"></i> Start
                        </button>
                        @endif

                        @if($order->status == 'processing')
                        <button class="btn btn-warning btn-sm continue-packing" data-id="{{$order->id}}" style="height:30px; width:auto;" data-toggle="tooltip" title="Continue Packing" data-placement="bottom">
                          <i class="fas fa-box"></i> Pack
                        </button>
                        @endif
                    </td>
                </tr>
            @endforeach
          </tbody>
        </table>
        <span style="float:right">{{$orders->links()}}</span>
        @else
          <h6 class="text-center">No orders assigned to you yet!</h6>
        @endif
      </div>
    </div>
</div>
@endsection

@push('styles')
  <link href="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.css')}}" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" />
  <style>
      div.dataTables_wrapper div.dataTables_paginate{
          display: none;
      }
      .badge {
          font-size: 0.8em;
      }
  </style>
@endpush

@push('scripts')

  <!-- Page level plugins -->
  <script src="{{asset('backend/vendor/datatables/jquery.dataTables.min.js')}}"></script>
  <script src="{{asset('backend/vendor/datatables/dataTables.bootstrap4.min.js')}}"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

  <!-- Page level custom scripts -->
  <script src="{{asset('backend/js/demo/datatables-demo.js')}}"></script>
  <script>

      $('#order-dataTable').DataTable( {
            "columnDefs":[
                {
                    "orderable":false,
                    "targets":[7]
                }
            ]
        } );

        // Status filter functionality
        $('#statusFilter').on('change', function() {
            var status = $(this).val();
            if (status === '') {
                $('tbody tr').show();
            } else {
                $('tbody tr').hide();
                $('tbody tr[data-status="' + status + '"]').show();
            }
        });

        // Start packing functionality
        $('.start-packing').click(function(e) {
            e.preventDefault();
            var order_id = $(this).data('id');
            var token = $("meta[name='csrf-token']").attr("content");

            $.ajax({
                url: "{{route('picker.orders.start-packing')}}",
                type: 'POST',
                data: {
                    "_token": token,
                    "order_id": order_id
                },
                success: function(response) {
                    if(response.status) {
                        swal({
                            title: "Success!",
                            text: response.message,
                            type: "success"
                        }, function() {
                            location.reload();
                        });
                    } else {
                        swal("Error!", response.message, "error");
                    }
                },
                error: function() {
                    swal("Error!", "Something went wrong", "error");
                }
            });
        });

        // Continue packing functionality
        $('.continue-packing').click(function(e) {
            e.preventDefault();
            var order_id = $(this).data('id');
            window.location.href = "{{route('picker.orders.pack', '')}}" + "/" + order_id;
        });

        // Order barcode scanner functionality
        $('#scanOrderBarcodeBtn').click(function() {
            $('#orderBarcodeScannerModal').modal('show');
            initializeOrderBarcodeScanner();
        });

        // Order Barcode Scanner Variables
        let orderCodeReader = null;
        let orderCurrentStream = null;
        let orderCurrentDeviceId = null;
        let orderAvailableCameras = [];

        function initializeOrderBarcodeScanner() {
            try {
                orderCodeReader = new ZXing.BrowserMultiFormatReader();
                orderCodeReader.listVideoInputDevices().then(cameras => {
                    orderAvailableCameras = cameras;
                    if (orderAvailableCameras.length > 0) {
                        $('#start-order-scanner').show();
                        if (orderAvailableCameras.length > 1) {
                            $('#switch-order-camera').show();
                        }
                    } else {
                        $('#order-scan-results').html('<div class="alert alert-warning">No cameras found. Please use manual entry.</div>');
                    }
                }).catch(err => {
                    console.error('Error initializing order barcode scanner:', err);
                    $('#order-scan-results').html('<div class="alert alert-danger">Camera access denied or not available. Please use manual entry.</div>');
                });
            } catch (err) {
                console.error('Error initializing order barcode scanner:', err);
                $('#order-scan-results').html('<div class="alert alert-danger">Barcode scanner not supported. Please use manual entry.</div>');
            }
        }

        $('#start-order-scanner').click(async function() {
            try {
                orderCurrentDeviceId = orderAvailableCameras[0].deviceId;

                orderCurrentStream = await orderCodeReader.decodeFromVideoDevice(orderCurrentDeviceId, 'order-scanner-video', (result, err) => {
                    if (result) {
                        handleOrderBarcodeResult(result.text);
                    }
                    if (err && !(err instanceof ZXing.NotFoundException)) {
                        console.error('Order barcode scanning error:', err);
                    }
                });

                $('#start-order-scanner').hide();
                $('#stop-order-scanner').show();
                $('#switch-order-camera').show();
            } catch (err) {
                console.error('Error starting order scanner:', err);
                $('#order-scan-results').html('<div class="alert alert-danger">Failed to start camera: ' + err.message + '</div>');
            }
        });

        $('#stop-order-scanner').click(function() {
            stopOrderScanner();
        });

        function stopOrderScanner() {
            if (orderCodeReader) {
                orderCodeReader.reset();
            }
            if (orderCurrentStream) {
                orderCurrentStream.getTracks().forEach(track => track.stop());
            }
            $('#start-order-scanner').show();
            $('#stop-order-scanner').hide();
        }

        $('#manual-order-search').click(function() {
            const barcode = $('#manual-order-barcode').val().trim();
            if (barcode) {
                handleOrderBarcodeResult(barcode);
                $('#manual-order-barcode').val('');
            }
        });

        $('#manual-order-barcode').keypress(function(e) {
            if (e.which === 13) { // Enter key
                $('#manual-order-search').click();
            }
        });

        function handleOrderBarcodeResult(barcode) {
            $.ajax({
                url: '/picker/orders/scan-barcode',
                type: 'POST',
                data: {
                    barcode: barcode,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.status) {
                        $('#order-scan-results').html(`
                            <div class="alert alert-success">
                                <strong>Order Found!</strong><br>
                                <strong>Order #:</strong> ${response.order.order_number}<br>
                                <strong>Customer:</strong> ${response.order.customer_name}<br>
                                <strong>Status:</strong> ${response.order.status}<br>
                                <strong>Total:</strong> $${response.order.total_amount}<br>
                                <strong>Date:</strong> ${response.order.created_at}<br>
                                <button class="btn btn-primary btn-sm mt-2" onclick="window.open('${response.order.url}', '_blank')">
                                    <i class="fas fa-eye"></i> View Details
                                </button>
                            </div>
                        `);
                        playBeepSound();
                    } else {
                        $('#order-scan-results').html(`
                            <div class="alert alert-warning">
                                <strong>Order Not Found</strong><br>
                                Barcode: ${barcode}
                            </div>
                        `);
                        playErrorSound();
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Failed to scan barcode';
                    if(xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    $('#order-scan-results').html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${errorMessage}
                        </div>
                    `);
                    playErrorSound();
                }
            });
        }

        function playBeepSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = 800;
                oscillator.type = 'square';

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (e) {
                console.log('Audio not supported');
            }
        }

        function playErrorSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = 300;
                oscillator.type = 'sawtooth';

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
            } catch (e) {
                console.log('Audio not supported');
            }
        }

        // Clean up when order modal is closed
        $('#orderBarcodeScannerModal').on('hidden.bs.modal', function() {
            stopOrderScanner();
            $('#order-scan-results').html('<p class="text-muted">No orders scanned yet</p>');
        });

  </script>
@endpush

<!-- Order Barcode Scanner Modal -->
<div class="modal fade" id="orderBarcodeScannerModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-qrcode"></i> Scan Order Barcode
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div id="order-scanner-container" style="position: relative;">
                            <video id="order-scanner-video" style="width: 100%; height: 300px; background: #000;"></video>
                            <div id="order-scanner-overlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); border: 2px solid #4D734E; width: 200px; height: 100px; pointer-events: none;"></div>
                        </div>
                        <div class="mt-3">
                            <button id="start-order-scanner" class="btn btn-success" style="display: none;">
                                <i class="fas fa-camera"></i> Start Camera
                            </button>
                            <button id="stop-order-scanner" class="btn btn-danger" style="display: none;">
                                <i class="fas fa-stop"></i> Stop Camera
                            </button>
                            <button id="switch-order-camera" class="btn btn-info" style="display: none;">
                                <i class="fas fa-sync"></i> Switch Camera
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6>Manual Entry</h6>
                        <div class="form-group">
                            <label for="manual-order-barcode">Order Number/Barcode:</label>
                            <input type="text" id="manual-order-barcode" class="form-control" placeholder="Enter order number or barcode">
                        </div>
                        <button id="manual-order-search" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> Search Order
                        </button>

                        <hr>

                        <h6>Scan Results</h6>
                        <div id="order-scan-results">
                            <p class="text-muted">No orders scanned yet</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
