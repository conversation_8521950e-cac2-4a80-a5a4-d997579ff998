<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use BarcodeBakery\Common\BCGFontPhp;
use BarcodeBakery\Common\BCGColor;
use BarcodeBakery\Common\BCGDrawing;
use BarcodeBakery\Barcode\BCGupca;

class Order extends Model
{
    protected $fillable = ['order_number', 'quote_number', 'barcode', 'barcode_path', 'user_id', 'salesman_id', 'sub_total', 'shipping_id', 'coupon', 'total_amount', 'amount_left', 'amount_paid','quantity', 'payment_method', 'payment_status', 'status', 'first_name', 'last_name', 'email', 'phone', 'country', 'post_code', 'address1', 'address2', 'is_draft', 'is_quick_order', 'original_order_id', 'signature_data', 'interaction_log_id', 'shipping_preference_id', 'return_status', 'credit_memo_id', 'delivery_instructions', 'shipping_method','delivery_method','picker_id'];

    protected $casts = [
        'is_draft' => 'boolean',
        'is_quick_order' => 'boolean',
    ];

    public function cart_info(){
        return $this->hasMany('App\Models\Cart','order_id','id')->where('status', '!=', 'distributed');
    }

    public function all_cart_items(){
        return $this->hasMany('App\Models\Cart','order_id','id');
    }
    public static function getAllOrder($id){
        return Order::with('cart_info')->find($id);
    }
    public static function countActiveOrder(){
        $data=Order::count();
        if($data){
            return $data;
        }
        return 0;
    }
    public function cart(){
        return $this->hasMany(Cart::class);
    }

    public function shipping(){
        return $this->belongsTo(Shipping::class,'shipping_id');
    }
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    public function salesman()
    {
        return $this->belongsTo(User::class, 'salesman_id');
    }

    public function signature()
    {
        return $this->hasOne(SignatureRecord::class);
    }

    public function interactionLog()
    {
        return $this->belongsTo(InteractionLog::class);
    }

    public function shippingPreference()
    {
        return $this->belongsTo(ShippingPreference::class);
    }

    public function route()
    {
        return $this->belongsToMany(DeliveryRoute::class, 'route_optimization_queue', 'order_id', 'route_id');
    }

    public function scopeNotDraft($query)
    {
        return $query->where('is_draft', false);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function customer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function orderPrices()
    {
        return $this->hasMany(OrderPrice::class);
    }

    public function histories()
    {
        return $this->hasMany(OrderHistory::class);
    }

    public function boxes()
    {
        return $this->hasMany(OrderBox::class);
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class);
    }

    public function backorders()
    {
        return $this->hasMany(Backorder::class, 'original_order_id');
    }

    public function picker()
    {
        return $this->belongsTo(User::class, 'picker_id');
    }

    public function getTotalBoxesAttribute()
    {
        return $this->boxes()->count();
    }

    public function getPackingProgressAttribute()
    {
        $totalItems = $this->cart_info->sum('quantity');
        $packedItems = $this->cart_info->sum('packed_quantity');

        if ($totalItems == 0) return 0;

        return round(($packedItems / $totalItems) * 100, 2);
    }

    public function hasBackorders()
    {
        return $this->backorders()->where('status', 'pending')->exists();
    }

    public function canBeCompleted()
    {
        try {
            // Check if all items are either packed or backordered
            $items = $this->cart_info;

            if ($items->isEmpty()) {
                \Log::warning('Attempting to complete order with no items', ['order_id' => $this->id]);
                return false;
            }

            foreach ($items as $item) {
                $packedQty = $item->packed_quantity ?? 0;
                $backorderQty = $item->backorder_quantity ?? 0;

                // Item is accounted for if it's packed, backordered, or returned
                $isAccountedFor = ($packedQty + $backorderQty) >= $item->quantity || $item->is_returned_by_picker;

                if (!$isAccountedFor) {
                    \Log::info('Order cannot be completed - item not fully accounted for', [
                        'order_id' => $this->id,
                        'item_id' => $item->id,
                        'required_qty' => $item->quantity,
                        'packed_qty' => $packedQty,
                        'backorder_qty' => $backorderQty,
                        'is_returned' => $item->is_returned_by_picker
                    ]);
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Error checking if order can be completed', [
                'order_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    public function completeOrder()
    {
        try {
            if (!$this->canBeCompleted()) {
                throw new \Exception('Order cannot be completed. Some items are not packed or backordered.');
            }

            // Additional validation
            if ($this->status === 'shipped') {
                throw new \Exception('Order is already completed.');
            }

            if ($this->status === 'cancelled') {
                throw new \Exception('Cannot complete a cancelled order.');
            }

            // Update order status
            $this->status = 'shipped';
            $this->save();

            // Update all boxes as sealed
            $this->boxes()->update(['is_sealed' => true, 'sealed_at' => now()]);

            // Log the completion
            \Log::info('Order completed successfully', [
                'order_id' => $this->id,
                'order_number' => $this->order_number,
                'completed_by' => auth()->id(),
                'completed_at' => now()
            ]);

            // Fire order completion event for office notifications
            if (auth()->user()) {
                event(new \App\Events\OrderCompleted($this, auth()->user()));
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Error completing order', [
                'order_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get full billing address
     */
    public function getFullAddress()
    {
        $address = [];
        if ($this->address1) $address[] = $this->address1;
        if ($this->address2) $address[] = $this->address2;
        if ($this->city) $address[] = $this->city;
        if ($this->state) $address[] = $this->state;
        if ($this->post_code) $address[] = $this->post_code;
        if ($this->country) $address[] = $this->country;

        return implode(', ', $address);
    }

    /**
     * Get full shipping address
     */
    public function getFullShippingAddress()
    {
        $address = [];
        if ($this->shipping_address) $address[] = $this->shipping_address;
        if ($this->shipping_city) $address[] = $this->shipping_city;
        if ($this->shipping_state) $address[] = $this->shipping_state;
        if ($this->shipping_zip) $address[] = $this->shipping_zip;

        return implode(', ', $address) ?: $this->getFullAddress();
    }

    /**
     * Get comprehensive box contents summary
     */
    public function getBoxContentsSummary()
    {
        $boxes = $this->boxes()->with(['items.product', 'items.color_name'])->get();

        return $boxes->map(function ($box) {
            $items = $box->items->map(function ($item) {
                return [
                    'product_id' => $item->product_id,
                    'product_name' => $item->product->title ?? 'Unknown Product',
                    'item_number' => $item->product->item_number ?? null,
                    'color_name' => $item->color_name->name ?? null,
                    'packed_quantity' => $item->packed_quantity,
                    'unit_price' => $item->unit_price ?? 0,
                    'line_total' => ($item->packed_quantity * ($item->unit_price ?? 0))
                ];
            });

            return [
                'box_number' => $box->box_number,
                'weight' => $box->weight,
                'dimensions' => [
                    'length' => $box->length,
                    'width' => $box->width,
                    'height' => $box->height
                ],
                'items' => $items,
                'item_count' => $items->count(),
                'total_quantity' => $items->sum('packed_quantity'),
                'total_value' => $items->sum('line_total')
            ];
        });
    }

    /**
     * Get packing completion statistics
     */
    public function getPackingStats()
    {
        $originalItems = $this->cart_info;
        $packedItems = $this->boxes()->with('items')->get()->flatMap->items;

        $originalQuantity = $originalItems->sum('quantity');
        $packedQuantity = $packedItems->sum('packed_quantity');

        return [
            'original_items' => $originalItems->count(),
            'original_quantity' => $originalQuantity,
            'packed_items' => $packedItems->count(),
            'packed_quantity' => $packedQuantity,
            'completion_percentage' => $originalQuantity > 0 ?
                round(($packedQuantity / $originalQuantity) * 100, 2) : 0,
            'total_boxes' => $this->boxes()->count(),
            'has_backorders' => $this->hasBackorders(),
            'shipping_cost' => $this->shipping_cost ?? 0
        ];
    }

    /**
     * Generate a unique quote number for the order
     */
    public static function generateQuoteNumber()
    {
        do {
            $quoteNumber = 'QTE-' . strtoupper(\Illuminate\Support\Str::random(10));
        } while (self::where('quote_number', $quoteNumber)->exists());

        return $quoteNumber;
    }

    /**
     * Get the quote number or generate one if it doesn't exist
     */
    public function getQuoteNumberAttribute($value)
    {
        if (!$value) {
            $quoteNumber = self::generateQuoteNumber();
            $this->update(['quote_number' => $quoteNumber]);
            return $quoteNumber;
        }
        return $value;
    }

    /**
     * Generate a unique barcode for the order
     */
    public function generateOrderBarcode()
    {
        if (!$this->barcode) {
            $this->barcode = $this->order_number;
            $this->barcode_path = $this->generateBarcodeImage($this->order_number);
            $this->save();
        }
        return $this->barcode_path;
    }

    /**
     * Generate barcode image using the same logic as ProductController
     */
    private function generateBarcodeImage($code)
    {
        try {
            // Use the same barcode generation logic as ProductController
            $path = "/order_barcodes/{$code}.png";

            // Create directory if it doesn't exist
            $fullPath = storage_path('app/public/order_barcodes');
            if (!file_exists($fullPath)) {
                mkdir($fullPath, 0755, true);
            }

            // ---------- configure ----------
            $font = new BCGFontPhp(2);
            $colorBlack = new BCGColor(0, 0, 0);
            $colorWhite = new BCGColor(255, 255, 255);

            // Barcode Part - using Code 128 instead of UPC-A for better compatibility with order numbers
            $barcodeGenerator = new \BarcodeBakery\Barcode\BCGcode128();
            $barcodeGenerator->setScale(2);
            $barcodeGenerator->setThickness(30);
            $barcodeGenerator->setForegroundColor($colorBlack);
            $barcodeGenerator->setBackgroundColor($colorWhite);
            $barcodeGenerator->setFont($font);
            $barcodeGenerator->parse($code);

            // Drawing Part
            $drawing = new BCGDrawing($barcodeGenerator, $colorWhite);
            $drawing->finish(BCGDrawing::IMG_FORMAT_PNG, storage_path('app/public') . $path);

            return $path;
        } catch (\Exception $e) {
            \Log::error('Failed to generate order barcode: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the barcode path or generate one if it doesn't exist
     */
    public function getBarcodePathAttribute($value)
    {
        if (!$value) {
            return $this->generateOrderBarcode();
        }
        return $value;
    }

}
