<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Box Label - {{$box->box_label}}</title>
    <style>
        /* 4x6 inch label styling */
        @page {
            size: 4in 6in;
            margin: 0.1in;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 8px;
            width: 3.8in;
            height: 5.8in;
            background: white;
            color: black;
        }

        .label-container {
            width: 100%;
            height: 100%;
            border: 2px solid #000;
            padding: 8px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 8px;
            margin-bottom: 8px;
        }

        .company-name {
            font-size: 16px;
            font-weight: bold;
            color: #4D734E;
            margin-bottom: 2px;
        }

        .box-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .order-info {
            font-size: 9px;
            margin-bottom: 8px;
        }

        .customer-section {
            border: 1px solid #000;
            padding: 6px;
            margin-bottom: 8px;
            background: #f8f9fa;
        }

        .customer-section h4 {
            margin: 0 0 4px 0;
            font-size: 10px;
            font-weight: bold;
            color: #4D734E;
        }

        .address {
            line-height: 1.2;
            font-size: 9px;
        }

        .items-section {
            flex: 1;
            border: 1px solid #000;
            padding: 6px;
            margin-bottom: 8px;
        }

        .items-section h4 {
            margin: 0 0 6px 0;
            font-size: 10px;
            font-weight: bold;
            color: #4D734E;
        }

        .item-list {
            font-size: 8px;
            line-height: 1.3;
            max-height: 120px;
            overflow: hidden;
        }

        .item {
            margin-bottom: 3px;
            padding: 2px;
            border-bottom: 1px dotted #ccc;
        }

        .item:last-child {
            border-bottom: none;
        }

        .item-number {
            font-weight: bold;
        }

        .item-qty {
            float: right;
            font-weight: bold;
        }

        .box-info {
            border: 1px solid #000;
            padding: 4px;
            margin-bottom: 6px;
            background: #e3f2fd;
        }

        .box-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4px;
            font-size: 8px;
        }

        .footer {
            border-top: 1px solid #000;
            padding-top: 4px;
            text-align: center;
            font-size: 8px;
        }

        .barcode-section {
            text-align: center;
            margin: 4px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            letter-spacing: 2px;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        .weight-section {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 4px;
            margin: 4px 0;
            text-align: center;
            font-size: 9px;
        }

        .urgent {
            background: #f8d7da;
            border: 1px solid #dc3545;
            color: #721c24;
            padding: 2px 4px;
            font-size: 8px;
            text-align: center;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="label-container">
        <div class="header">
            <div class="company-name">LAMART</div>
            <div class="box-title">{{$box->box_label}}</div>
            <div class="order-info">
                Order #{{$order->order_number}} | {{$order->created_at->format('M d, Y')}}
            </div>
        </div>

        @if($order->delivery_method == 'express' || $order->delivery_method == 'urgent')
        <div class="urgent">
            ⚡ {{strtoupper($order->delivery_method)}} DELIVERY
        </div>
        @endif

        <div class="customer-section">
            <h4>📦 SHIP TO:</h4>
            <div class="address">
                <strong>{{$order->first_name}} {{$order->last_name}}</strong><br>
                {{$order->address1}}<br>
                @if($order->address2){{$order->address2}}<br>@endif
                {{$order->country}}, {{$order->post_code}}<br>
                📞 {{$order->phone}}
            </div>
        </div>

        <div class="box-info">
            <div class="box-info-grid">
                <div><strong>Dimensions:</strong></div>
                <div>
                    @if($box->length && $box->width && $box->height)
                        {{$box->length}}" × {{$box->width}}" × {{$box->height}}"
                    @else
                        Not specified
                    @endif
                </div>
                <div><strong>Weight:</strong></div>
                <div>{{$box->weight ? $box->weight . ' lbs' : 'TBD'}}</div>
                <div><strong>Items:</strong></div>
                <div>{{$box->items->sum('packed_quantity')}} items</div>
                <div><strong>Delivery:</strong></div>
                <div>{{ucfirst($order->delivery_method ?? 'Standard')}}</div>
            </div>
        </div>

        <div class="items-section">
            <h4>📋 BOX CONTENTS:</h4>
            <div class="item-list">
                @foreach($box->items as $item)
                <div class="item">
                    <span class="item-number">
                        @if($item->product && $item->product->item_number)
                            {{$item->product->item_number}}
                        @else
                            Item-{{$item->product_id}}
                        @endif
                    </span>
                    <span class="item-qty">Qty: {{$item->packed_quantity ?? $item->quantity}}</span><br>
                    <div style="font-size: 7px; color: #666;">
                        @if($item->product && $item->product->title)
                            {{$item->product->title}}
                        @else
                            Product ID: {{$item->product_id}}
                        @endif
                        @if($item->color_name && $item->color_name->name)
                            - {{$item->color_name->name}}
                        @elseif($item->color)
                            - Color ID: {{$item->color}}
                        @endif
                    </div>
                </div>
                @endforeach

                @if($box->items->count() == 0)
                <div style="text-align: center; color: #666; font-style: italic;">
                    No items assigned to this box yet
                </div>
                @endif
            </div>
        </div>

        @if($box->weight)
        <div class="weight-section">
            <strong>TOTAL WEIGHT: {{$box->weight}} lbs</strong>
            @if($box->weight > 50)
                <br><span style="color: #dc3545;">⚠️ HEAVY PACKAGE</span>
            @endif
        </div>
        @endif

        <div class="barcode-section">
            {{$order->order_number}}-{{$box->box_number}}
        </div>

        @if($order->barcode_path)
        <div class="order-barcode-section" style="text-align: center; margin: 4px 0; border-top: 1px solid #000; padding-top: 4px;">
            <div style="font-size: 7px; margin-bottom: 2px;">ORDER BARCODE</div>
            <img src="{{asset('storage' . $order->barcode_path)}}" alt="Order Barcode" style="height: 30px;">
        </div>
        @endif

        <div class="footer">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 7px;">
                <div>
                    <strong>Packed:</strong> {{now()->format('m/d/Y H:i')}}<br>
                    <strong>Picker:</strong> {{Auth::user()->first_name}} {{Auth::user()->last_name}}
                </div>
                <div>
                    <strong>Handle:</strong>
                    @if($box->weight && $box->weight > 30)
                        HEAVY
                    @elseif($box->items->contains(function($item) { return str_contains(strtolower($item->product->title), 'fragile') || str_contains(strtolower($item->product->title), 'glass'); }))
                        FRAGILE
                    @else
                        STANDARD
                    @endif
                    <br>
                    <strong>Delivery:</strong> {{strtoupper($order->delivery_method ?? 'STD')}}
                </div>
            </div>
        </div>

        @if($box->notes)
        <div style="border-top: 1px solid #000; padding-top: 2px; font-size: 7px; color: #666;">
            <strong>Notes:</strong> {{$box->notes}}
        </div>
        @endif
    </div>

    <div class="no-print" style="position: fixed; bottom: 10px; right: 10px;">
        <button onclick="window.print()" style="padding: 8px 16px; background: #4D734E; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 5px;">
            🖨️ Print Label
        </button>
        <button onclick="window.close()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
            ✖️ Close
        </button>
    </div>
</body>
</html>
