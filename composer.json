{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "barcode-bakery/barcode-1d": "^7.0", "barcode-bakery/barcode-common": "^7.0", "barryvdh/laravel-dompdf": "^2.0", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^3.11", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.6", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "maatwebsite/excel": "^3.1", "pusher/pusher-php-server": "^7.2", "quickbooks/v3-php-sdk": "^6.2", "spatie/laravel-newsletter": "^5.1", "srmklive/paypal": "^3.0", "unisharp/laravel-filemanager": "^2.6", "yajra/laravel-datatables": "^10.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Touhidurabir\\Filterable\\": "packages/laravel-filterable/src/"}, "files": ["app/Http/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/Http/Helpers.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}