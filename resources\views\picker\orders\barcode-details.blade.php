@extends('backend.layouts.master')

@section('title','Order Details - Barcode Scan')

@section('main-content')
<div class="card">
    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-qrcode"></i> Order Details - Scanned from Barcode
        </h6>
        <div class="btn-group">
            <a href="{{route('picker.orders')}}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
            <a href="{{route('picker.orders.pack', $order->id)}}" class="btn btn-primary btn-sm">
                <i class="fas fa-box"></i> Pack Order
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Order Information -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-left-primary">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-shopping-cart"></i> Order Information
                        </h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Order Number:</strong></td>
                                <td>{{$order->order_number}}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge badge-{{$order->status == 'processing' ? 'warning' : ($order->status == 'shipped' ? 'success' : 'info')}}">
                                        {{ucfirst($order->status)}}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td>${{number_format($order->total_amount, 2)}}</td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{$order->created_at->format('M d, Y g:i A')}}</td>
                            </tr>
                            @if($order->picker)
                            <tr>
                                <td><strong>Picker:</strong></td>
                                <td>{{$order->picker->name}}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-left-success">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            <i class="fas fa-user"></i> Customer Information
                        </h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{$order->first_name}} {{$order->last_name}}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{$order->email}}</td>
                            </tr>
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td>{{$order->phone}}</td>
                            </tr>
                            <tr>
                                <td><strong>Address:</strong></td>
                                <td>
                                    {{$order->address1}}<br>
                                    @if($order->address2){{$order->address2}}<br>@endif
                                    {{$order->country}} {{$order->post_code}}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Barcode -->
        @if($order->barcode_path)
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-left-info">
                    <div class="card-body text-center">
                        <h5 class="card-title text-info">
                            <i class="fas fa-barcode"></i> Order Barcode
                        </h5>
                        <img src="{{asset('storage' . $order->barcode_path)}}" alt="Order Barcode" class="img-fluid" style="max-height: 100px;">
                        <p class="mt-2 text-muted">{{$order->order_number}}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Order Items -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Order Items ({{$order->cart_info->count()}} items)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Item #</th>
                                        <th>Product</th>
                                        <th>Color</th>
                                        <th>Quantity</th>
                                        <th>Packed</th>
                                        <th>Price</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->cart_info as $item)
                                    <tr>
                                        <td>{{$item->product->item_number ?? 'N/A'}}</td>
                                        <td>{{$item->product->title ?? 'N/A'}}</td>
                                        <td>{{$item->color_name->title ?? 'N/A'}}</td>
                                        <td>{{$item->quantity}}</td>
                                        <td>
                                            <span class="badge badge-{{$item->packed_quantity == $item->quantity ? 'success' : ($item->packed_quantity > 0 ? 'warning' : 'secondary')}}">
                                                {{$item->packed_quantity ?? 0}}
                                            </span>
                                        </td>
                                        <td>${{number_format($item->price, 2)}}</td>
                                        <td>${{number_format($item->price * $item->quantity, 2)}}</td>
                                        <td>
                                            @if($item->packed_quantity == $item->quantity)
                                                <span class="badge badge-success">Complete</span>
                                            @elseif($item->packed_quantity > 0)
                                                <span class="badge badge-warning">Partial</span>
                                            @else
                                                <span class="badge badge-secondary">Pending</span>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boxes Information -->
        @if($order->boxes->count() > 0)
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-boxes"></i> Packed Boxes ({{$order->boxes->count()}} boxes)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($order->boxes as $box)
                            <div class="col-md-6 mb-3">
                                <div class="card border-left-warning">
                                    <div class="card-body">
                                        <h6 class="card-title">{{$box->box_label}}</h6>
                                        <p class="card-text">
                                            <strong>Items:</strong> {{$box->items->count()}}<br>
                                            @if($box->weight)
                                            <strong>Weight:</strong> {{$box->weight}} lbs<br>
                                            @endif
                                            @if($box->notes)
                                            <strong>Notes:</strong> {{$box->notes}}
                                            @endif
                                        </p>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{route('picker.orders.box-label', $box->id)}}" class="btn btn-outline-primary" target="_blank">
                                                <i class="fas fa-print"></i> Print Label
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <div class="btn-group" role="group">
                    <a href="{{route('picker.orders.pack', $order->id)}}" class="btn btn-primary btn-lg">
                        <i class="fas fa-box"></i> Pack This Order
                    </a>
                    @if($order->boxes->count() > 0)
                    <a href="{{route('picker.orders.all-labels', $order->id)}}" class="btn btn-success btn-lg" target="_blank">
                        <i class="fas fa-print"></i> Print All Labels
                    </a>
                    @endif
                    <a href="{{route('picker.orders')}}" class="btn btn-secondary btn-lg">
                        <i class="fas fa-list"></i> Back to Orders
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .card {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border: 1px solid #e3e6f0;
    }
    
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    
    .table-borderless td {
        border: none;
        padding: 0.5rem 0;
    }
</style>
@endpush
